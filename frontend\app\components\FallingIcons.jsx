"use client";

import { useState, useEffect, memo } from 'react';
import { motion } from 'framer-motion';

/**
 * FallingIcons component - simplified and stable
 * Creates a background animation of falling icons/particles
 */
function FallingIcons({ count = 12, icons = ['✉️', '📱', '💬', '🔔', '📞', '🗓️', '📊', '💼'] }) {
  const [particles, setParticles] = useState([]);
  const [isClient, setIsClient] = useState(false);

  // Generate particles only once on mount
  useEffect(() => {
    setIsClient(true);

    // Simple particle generation without complex dependencies
    const newParticles = [];
    const particleCount = Math.min(count, 15); // Limit particles for performance

    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: -10 - Math.random() * 100,
        size: 16 + Math.random() * 16,
        duration: 12 + Math.random() * 8,
        delay: Math.random() * 5,
        rotate: Math.random() * 360,
        icon: icons[Math.floor(Math.random() * icons.length)],
        opacity: 0.1 + Math.random() * 0.2,
      });
    }

    setParticles(newParticles);
  }, []); // Empty dependency array - only run once

  // Don't render anything during SSR or if no particles
  if (!isClient || particles.length === 0) return null;

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute text-white/20 select-none will-change-transform"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            fontSize: `${particle.size}px`,
          }}
          initial={{
            y: particle.y,
            x: particle.x,
            rotate: particle.rotate,
            opacity: 0
          }}
          animate={{
            y: '120vh',
            rotate: particle.rotate + 360,
            opacity: [0, particle.opacity, particle.opacity, 0]
          }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            repeat: Infinity,
            ease: 'linear'
          }}
        >
          {particle.icon}
        </motion.div>
      ))}
    </div>
  );
}

// Memoize the component to prevent unnecessary re-renders
export default memo(FallingIcons);
